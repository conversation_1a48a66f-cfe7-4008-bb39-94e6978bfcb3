
let config = {
	//不拦截页面路径
    whiteList: [
    	'/pages/public/login',
		'/pages/public/forget_password',
		'/pages/public/register',
		'/pages/public/wechat_login',
		'/pages/admin/index',
		'/pages/index/index',
		'/pages/index/list',
		'/pages/subPack/index/detail',
		//一行一个
    ],
    token : 'token', //本地存储token的变量名
    login_page : '/pages/public/wechat_login',  //拦截后跳转的微信登录页面
}
export default config