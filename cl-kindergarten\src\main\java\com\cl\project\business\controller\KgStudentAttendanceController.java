package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgStudentAttendance;
import com.cl.project.business.service.IKgStudentAttendanceService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 学生考勤记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/student-attendance")
public class KgStudentAttendanceController extends BaseController
{
    @Autowired
    private IKgStudentAttendanceService kgStudentAttendanceService;

    /**
     * 查询学生考勤记录列表
     */
    @SaCheckPermission("kg:attendance:student:list")
    @GetMapping("/list")
    public TableDataInfo list(KgStudentAttendance kgStudentAttendance)
    {
        startPage();
        List<KgStudentAttendance> list = kgStudentAttendanceService.selectKgStudentAttendanceList(kgStudentAttendance);
        return getDataTable(list);
    }

    /**
     * 导出学生考勤记录列表
     */
    @SaCheckPermission("kg:attendance:student:query")
    @Log(title = "学生考勤记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgStudentAttendance kgStudentAttendance)
    {
        List<KgStudentAttendance> list = kgStudentAttendanceService.selectKgStudentAttendanceList(kgStudentAttendance);
        ExcelUtil<KgStudentAttendance> util = new ExcelUtil<KgStudentAttendance>(KgStudentAttendance.class);
        return util.exportExcel(list, "attendance");
    }

    /**
     * 获取学生考勤记录详细信息
     */
    @SaCheckPermission("kg:attendance:student:query")
    @GetMapping(value = "/{attendanceId}")
    public AjaxResult getInfo(@PathVariable("attendanceId") Long attendanceId)
    {
        return AjaxResult.success(kgStudentAttendanceService.selectKgStudentAttendanceById(attendanceId));
    }

    /**
     * 新增学生考勤记录
     */
    @SaCheckPermission("kg:attendance:student:checkin")
    @Log(title = "学生考勤记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgStudentAttendance kgStudentAttendance)
    {
        return toAjax(kgStudentAttendanceService.insertKgStudentAttendance(kgStudentAttendance));
    }

    /**
     * 修改学生考勤记录
     */
    @SaCheckPermission("kg:attendance:student:confirm")
    @Log(title = "学生考勤记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgStudentAttendance kgStudentAttendance)
    {
        return toAjax(kgStudentAttendanceService.updateKgStudentAttendance(kgStudentAttendance));
    }

    /**
     * 删除学生考勤记录
     */
    @SaCheckPermission("kg:attendance:student:checkin")
    @Log(title = "学生考勤记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{attendanceIds}")
    public AjaxResult remove(@PathVariable Long[] attendanceIds)
    {
        return toAjax(kgStudentAttendanceService.deleteKgStudentAttendanceByIds(attendanceIds));
    }
}
