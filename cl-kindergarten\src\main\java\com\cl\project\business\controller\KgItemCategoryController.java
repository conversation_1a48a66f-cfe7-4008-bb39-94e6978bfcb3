package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgItemCategory;
import com.cl.project.business.service.IKgItemCategoryService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 物品类别Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/category")
public class KgItemCategoryController extends BaseController
{
    @Autowired
    private IKgItemCategoryService kgItemCategoryService;

    /**
     * 查询物品类别列表
     */
    @SaCheckPermission("business:category:list")
    @GetMapping("/list")
    public TableDataInfo list(KgItemCategory kgItemCategory)
    {
        startPage();
        List<KgItemCategory> list = kgItemCategoryService.selectKgItemCategoryList(kgItemCategory);
        return getDataTable(list);
    }

    /**
     * 导出物品类别列表
     */
    @SaCheckPermission("business:category:export")
    @Log(title = "物品类别", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgItemCategory kgItemCategory)
    {
        List<KgItemCategory> list = kgItemCategoryService.selectKgItemCategoryList(kgItemCategory);
        ExcelUtil<KgItemCategory> util = new ExcelUtil<KgItemCategory>(KgItemCategory.class);
        return util.exportExcel(list, "category");
    }

    /**
     * 获取物品类别详细信息
     */
    @SaCheckPermission("business:category:query")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        return AjaxResult.success(kgItemCategoryService.selectKgItemCategoryById(categoryId));
    }

    /**
     * 新增物品类别
     */
    @SaCheckPermission("business:category:add")
    @Log(title = "物品类别", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgItemCategory kgItemCategory)
    {
        return toAjax(kgItemCategoryService.insertKgItemCategory(kgItemCategory));
    }

    /**
     * 修改物品类别
     */
    @SaCheckPermission("business:category:edit")
    @Log(title = "物品类别", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgItemCategory kgItemCategory)
    {
        return toAjax(kgItemCategoryService.updateKgItemCategory(kgItemCategory));
    }

    /**
     * 删除物品类别
     */
    @SaCheckPermission("business:category:remove")
    @Log(title = "物品类别", businessType = BusinessType.DELETE)
	@DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable Long[] categoryIds)
    {
        return toAjax(kgItemCategoryService.deleteKgItemCategoryByIds(categoryIds));
    }
}
