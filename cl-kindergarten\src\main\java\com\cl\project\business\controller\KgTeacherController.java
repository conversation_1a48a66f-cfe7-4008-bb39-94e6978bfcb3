package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgTeacher;
import com.cl.project.business.service.IKgTeacherService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 教师信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/teacher")
public class KgTeacherController extends BaseController
{
    @Autowired
    private IKgTeacherService kgTeacherService;

    /**
     * 查询教师信息列表
     */
//    @SaCheckPermission("kg:teacher:info:list")
    @GetMapping("/list")
    public TableDataInfo list(KgTeacher kgTeacher)
    {
        startPage();
        List<KgTeacher> list = kgTeacherService.selectKgTeacherList(kgTeacher);
        return getDataTable(list);
    }

    /**
     * 导出教师信息列表
     */
    @SaCheckPermission("kg:teacher:info:list")
    @Log(title = "教师信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgTeacher kgTeacher)
    {
        List<KgTeacher> list = kgTeacherService.selectKgTeacherList(kgTeacher);
        ExcelUtil<KgTeacher> util = new ExcelUtil<KgTeacher>(KgTeacher.class);
        return util.exportExcel(list, "teacher");
    }

    /**
     * 获取教师信息详细信息
     */
    @SaCheckPermission("kg:teacher:info:list")
    @GetMapping(value = "/{teacherId}")
    public AjaxResult getInfo(@PathVariable("teacherId") Long teacherId)
    {
        return AjaxResult.success(kgTeacherService.selectKgTeacherById(teacherId));
    }

    /**
     * 新增教师信息
     */
    @SaCheckPermission("kg:teacher:info:add")
    @Log(title = "教师信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgTeacher kgTeacher)
    {
        return toAjax(kgTeacherService.insertKgTeacher(kgTeacher));
    }

    /**
     * 修改教师信息
     */
    @SaCheckPermission("kg:teacher:info:edit")
    @Log(title = "教师信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgTeacher kgTeacher)
    {
        return toAjax(kgTeacherService.updateKgTeacher(kgTeacher));
    }

    /**
     * 删除教师信息
     */
    @SaCheckPermission("kg:teacher:info:remove")
    @Log(title = "教师信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{teacherIds}")
    public AjaxResult remove(@PathVariable Long[] teacherIds)
    {
        return toAjax(kgTeacherService.deleteKgTeacherByIds(teacherIds));
    }
}
