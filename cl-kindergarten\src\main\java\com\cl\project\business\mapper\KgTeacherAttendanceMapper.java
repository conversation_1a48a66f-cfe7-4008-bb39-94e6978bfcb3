package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgTeacherAttendance;

/**
 * 教师考勤记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgTeacherAttendanceMapper 
{
    /**
     * 查询教师考勤记录
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 教师考勤记录
     */
    public KgTeacherAttendance selectKgTeacherAttendanceById(Long attendanceId);

    /**
     * 查询教师考勤记录列表
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 教师考勤记录集合
     */
    public List<KgTeacherAttendance> selectKgTeacherAttendanceList(KgTeacherAttendance kgTeacherAttendance);

    /**
     * 新增教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    public int insertKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance);

    /**
     * 修改教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    public int updateKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance);

    /**
     * 删除教师考勤记录
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 结果
     */
    public int deleteKgTeacherAttendanceById(Long attendanceId);

    /**
     * 批量删除教师考勤记录
     * 
     * @param attendanceIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgTeacherAttendanceByIds(Long[] attendanceIds);
}
