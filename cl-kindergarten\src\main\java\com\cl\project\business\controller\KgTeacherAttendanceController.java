package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgTeacherAttendance;
import com.cl.project.business.service.IKgTeacherAttendanceService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 教师考勤记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/teacher-attendance")
public class KgTeacherAttendanceController extends BaseController
{
    @Autowired
    private IKgTeacherAttendanceService kgTeacherAttendanceService;

    /**
     * 查询教师考勤记录列表
     */
    @SaCheckPermission("kg:attendance:teacher:list")
    @GetMapping("/list")
    public TableDataInfo list(KgTeacherAttendance kgTeacherAttendance)
    {
        startPage();
        List<KgTeacherAttendance> list = kgTeacherAttendanceService.selectKgTeacherAttendanceList(kgTeacherAttendance);
        return getDataTable(list);
    }

    /**
     * 导出教师考勤记录列表
     */
    @SaCheckPermission("kg:attendance:teacher:query")
    @Log(title = "教师考勤记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgTeacherAttendance kgTeacherAttendance)
    {
        List<KgTeacherAttendance> list = kgTeacherAttendanceService.selectKgTeacherAttendanceList(kgTeacherAttendance);
        ExcelUtil<KgTeacherAttendance> util = new ExcelUtil<KgTeacherAttendance>(KgTeacherAttendance.class);
        return util.exportExcel(list, "attendance");
    }

    /**
     * 获取教师考勤记录详细信息
     */
    @SaCheckPermission("kg:attendance:teacher:query")
    @GetMapping(value = "/{attendanceId}")
    public AjaxResult getInfo(@PathVariable("attendanceId") Long attendanceId)
    {
        return AjaxResult.success(kgTeacherAttendanceService.selectKgTeacherAttendanceById(attendanceId));
    }

    /**
     * 新增教师考勤记录
     */
    @SaCheckPermission("kg:attendance:teacher:checkin")
    @Log(title = "教师考勤记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgTeacherAttendance kgTeacherAttendance)
    {
        return toAjax(kgTeacherAttendanceService.insertKgTeacherAttendance(kgTeacherAttendance));
    }

    /**
     * 修改教师考勤记录
     */
    @SaCheckPermission("kg:attendance:teacher:checkout")
    @Log(title = "教师考勤记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgTeacherAttendance kgTeacherAttendance)
    {
        return toAjax(kgTeacherAttendanceService.updateKgTeacherAttendance(kgTeacherAttendance));
    }

    /**
     * 删除教师考勤记录
     */
    @SaCheckPermission("kg:attendance:teacher:checkin")
    @Log(title = "教师考勤记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{attendanceIds}")
    public AjaxResult remove(@PathVariable Long[] attendanceIds)
    {
        return toAjax(kgTeacherAttendanceService.deleteKgTeacherAttendanceByIds(attendanceIds));
    }
}
