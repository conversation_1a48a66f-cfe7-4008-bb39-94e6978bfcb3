<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgStudentAttendanceMapper">
    
    <resultMap type="KgStudentAttendance" id="KgStudentAttendanceResult">
        <result property="attendanceId"    column="attendance_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="classId"    column="class_id"    />
        <result property="attendanceDate"    column="attendance_date"    />
        <result property="checkInTime"    column="check_in_time"    />
        <result property="checkOutTime"    column="check_out_time"    />
        <result property="attendanceStatus"    column="attendance_status"    />
        <result property="absenceReason"    column="absence_reason"    />
        <result property="sickDetail"    column="sick_detail"    />
        <result property="leaveDetail"    column="leave_detail"    />
        <result property="checkInMethod"    column="check_in_method"    />
        <result property="checkOutMethod"    column="check_out_method"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="isConfirmed"    column="is_confirmed"    />
        <result property="confirmedBy"    column="confirmed_by"    />
        <result property="confirmedTime"    column="confirmed_time"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgStudentAttendanceVo">
        select attendance_id, student_id, class_id, attendance_date, check_in_time, check_out_time, attendance_status, absence_reason, sick_detail, leave_detail, check_in_method, check_out_method, operator_id, is_confirmed, confirmed_by, confirmed_time, com_id, create_by, create_time, update_by, update_time, remark from kg_student_attendance
    </sql>

    <select id="selectKgStudentAttendanceList" parameterType="KgStudentAttendance" resultMap="KgStudentAttendanceResult">
        <include refid="selectKgStudentAttendanceVo"/>
        <where>  
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="attendanceDate != null "> and attendance_date = #{attendanceDate}</if>
            <if test="checkInTime != null "> and check_in_time = #{checkInTime}</if>
            <if test="checkOutTime != null "> and check_out_time = #{checkOutTime}</if>
            <if test="attendanceStatus != null  and attendanceStatus != ''"> and attendance_status = #{attendanceStatus}</if>
            <if test="absenceReason != null  and absenceReason != ''"> and absence_reason = #{absenceReason}</if>
            <if test="sickDetail != null  and sickDetail != ''"> and sick_detail = #{sickDetail}</if>
            <if test="leaveDetail != null  and leaveDetail != ''"> and leave_detail = #{leaveDetail}</if>
            <if test="checkInMethod != null  and checkInMethod != ''"> and check_in_method = #{checkInMethod}</if>
            <if test="checkOutMethod != null  and checkOutMethod != ''"> and check_out_method = #{checkOutMethod}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="isConfirmed != null "> and is_confirmed = #{isConfirmed}</if>
            <if test="confirmedBy != null "> and confirmed_by = #{confirmedBy}</if>
            <if test="confirmedTime != null "> and confirmed_time = #{confirmedTime}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgStudentAttendanceById" parameterType="Long" resultMap="KgStudentAttendanceResult">
        <include refid="selectKgStudentAttendanceVo"/>
        where attendance_id = #{attendanceId}
    </select>
        
    <insert id="insertKgStudentAttendance" parameterType="KgStudentAttendance" useGeneratedKeys="true" keyProperty="attendanceId">
        insert into kg_student_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="attendanceDate != null">attendance_date,</if>
            <if test="checkInTime != null">check_in_time,</if>
            <if test="checkOutTime != null">check_out_time,</if>
            <if test="attendanceStatus != null">attendance_status,</if>
            <if test="absenceReason != null">absence_reason,</if>
            <if test="sickDetail != null">sick_detail,</if>
            <if test="leaveDetail != null">leave_detail,</if>
            <if test="checkInMethod != null">check_in_method,</if>
            <if test="checkOutMethod != null">check_out_method,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="isConfirmed != null">is_confirmed,</if>
            <if test="confirmedBy != null">confirmed_by,</if>
            <if test="confirmedTime != null">confirmed_time,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="attendanceDate != null">#{attendanceDate},</if>
            <if test="checkInTime != null">#{checkInTime},</if>
            <if test="checkOutTime != null">#{checkOutTime},</if>
            <if test="attendanceStatus != null">#{attendanceStatus},</if>
            <if test="absenceReason != null">#{absenceReason},</if>
            <if test="sickDetail != null">#{sickDetail},</if>
            <if test="leaveDetail != null">#{leaveDetail},</if>
            <if test="checkInMethod != null">#{checkInMethod},</if>
            <if test="checkOutMethod != null">#{checkOutMethod},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="isConfirmed != null">#{isConfirmed},</if>
            <if test="confirmedBy != null">#{confirmedBy},</if>
            <if test="confirmedTime != null">#{confirmedTime},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgStudentAttendance" parameterType="KgStudentAttendance">
        update kg_student_attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="attendanceDate != null">attendance_date = #{attendanceDate},</if>
            <if test="checkInTime != null">check_in_time = #{checkInTime},</if>
            <if test="checkOutTime != null">check_out_time = #{checkOutTime},</if>
            <if test="attendanceStatus != null">attendance_status = #{attendanceStatus},</if>
            <if test="absenceReason != null">absence_reason = #{absenceReason},</if>
            <if test="sickDetail != null">sick_detail = #{sickDetail},</if>
            <if test="leaveDetail != null">leave_detail = #{leaveDetail},</if>
            <if test="checkInMethod != null">check_in_method = #{checkInMethod},</if>
            <if test="checkOutMethod != null">check_out_method = #{checkOutMethod},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="isConfirmed != null">is_confirmed = #{isConfirmed},</if>
            <if test="confirmedBy != null">confirmed_by = #{confirmedBy},</if>
            <if test="confirmedTime != null">confirmed_time = #{confirmedTime},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where attendance_id = #{attendanceId}
    </update>

    <delete id="deleteKgStudentAttendanceById" parameterType="Long">
        delete from kg_student_attendance where attendance_id = #{attendanceId}
    </delete>

    <delete id="deleteKgStudentAttendanceByIds" parameterType="String">
        delete from kg_student_attendance where attendance_id in 
        <foreach item="attendanceId" collection="array" open="(" separator="," close=")">
            #{attendanceId}
        </foreach>
    </delete>
    
</mapper>