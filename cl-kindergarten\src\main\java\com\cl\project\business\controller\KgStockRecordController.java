package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgStockRecord;
import com.cl.project.business.service.IKgStockRecordService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 库存变动记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/record")
public class KgStockRecordController extends BaseController
{
    @Autowired
    private IKgStockRecordService kgStockRecordService;

    /**
     * 查询库存变动记录列表
     */
    @SaCheckPermission("business:record:list")
    @GetMapping("/list")
    public TableDataInfo list(KgStockRecord kgStockRecord)
    {
        startPage();
        List<KgStockRecord> list = kgStockRecordService.selectKgStockRecordList(kgStockRecord);
        return getDataTable(list);
    }

    /**
     * 导出库存变动记录列表
     */
    @SaCheckPermission("business:record:export")
    @Log(title = "库存变动记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgStockRecord kgStockRecord)
    {
        List<KgStockRecord> list = kgStockRecordService.selectKgStockRecordList(kgStockRecord);
        ExcelUtil<KgStockRecord> util = new ExcelUtil<KgStockRecord>(KgStockRecord.class);
        return util.exportExcel(list, "record");
    }

    /**
     * 获取库存变动记录详细信息
     */
    @SaCheckPermission("business:record:query")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return AjaxResult.success(kgStockRecordService.selectKgStockRecordById(recordId));
    }

    /**
     * 新增库存变动记录
     */
    @SaCheckPermission("business:record:add")
    @Log(title = "库存变动记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgStockRecord kgStockRecord)
    {
        return toAjax(kgStockRecordService.insertKgStockRecord(kgStockRecord));
    }

    /**
     * 修改库存变动记录
     */
    @SaCheckPermission("business:record:edit")
    @Log(title = "库存变动记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgStockRecord kgStockRecord)
    {
        return toAjax(kgStockRecordService.updateKgStockRecord(kgStockRecord));
    }

    /**
     * 删除库存变动记录
     */
    @SaCheckPermission("business:record:remove")
    @Log(title = "库存变动记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds)
    {
        return toAjax(kgStockRecordService.deleteKgStockRecordByIds(recordIds));
    }
}
