package com.cl.project.business.domain;

import com.cl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 班级信息对象 kg_class
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgClass extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 班级ID */
    private Long classId;

    /** 班级名称 */
    @Excel(name = "班级名称")
    private String className;

    /** 班级类型（托班、小班、中班、大班） */
    @Excel(name = "班级类型", readConverterExp = "托=班、小班、中班、大班")
    private String classType;

    /** 班级容量 */
    @Excel(name = "班级容量")
    private Long capacity;

    /** 当前人数 */
    @Excel(name = "当前人数")
    private Long currentCount;

    /** 班主任ID，关联kg_teacher.teacher_id */
    @Excel(name = "班主任ID，关联kg_teacher.teacher_id")
    private Long headTeacherId;

    /** 副班主任ID，关联kg_teacher.teacher_id */
    @Excel(name = "副班主任ID，关联kg_teacher.teacher_id")
    private Long assistantTeacherId;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setClassId(Long classId) 
    {
        this.classId = classId;
    }

    public Long getClassId() 
    {
        return classId;
    }
    public void setClassName(String className) 
    {
        this.className = className;
    }

    public String getClassName() 
    {
        return className;
    }
    public void setClassType(String classType) 
    {
        this.classType = classType;
    }

    public String getClassType() 
    {
        return classType;
    }
    public void setCapacity(Long capacity) 
    {
        this.capacity = capacity;
    }

    public Long getCapacity() 
    {
        return capacity;
    }
    public void setCurrentCount(Long currentCount) 
    {
        this.currentCount = currentCount;
    }

    public Long getCurrentCount() 
    {
        return currentCount;
    }
    public void setHeadTeacherId(Long headTeacherId) 
    {
        this.headTeacherId = headTeacherId;
    }

    public Long getHeadTeacherId() 
    {
        return headTeacherId;
    }
    public void setAssistantTeacherId(Long assistantTeacherId) 
    {
        this.assistantTeacherId = assistantTeacherId;
    }

    public Long getAssistantTeacherId() 
    {
        return assistantTeacherId;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("classId", getClassId())
            .append("className", getClassName())
            .append("classType", getClassType())
            .append("capacity", getCapacity())
            .append("currentCount", getCurrentCount())
            .append("headTeacherId", getHeadTeacherId())
            .append("assistantTeacherId", getAssistantTeacherId())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
