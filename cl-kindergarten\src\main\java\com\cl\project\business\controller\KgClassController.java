package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgClass;
import com.cl.project.business.service.IKgClassService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 班级信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/class")
public class KgClassController extends BaseController
{
    @Autowired
    private IKgClassService kgClassService;

    /**
     * 查询班级信息列表
     */
    @SaCheckPermission("kg:student:class:list")
    @GetMapping("/list")
    public TableDataInfo list(KgClass kgClass)
    {
        startPage();
        List<KgClass> list = kgClassService.selectKgClassList(kgClass);
        return getDataTable(list);
    }

    /**
     * 导出班级信息列表
     */
    @SaCheckPermission("kg:student:class:list")
    @Log(title = "班级信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgClass kgClass)
    {
        List<KgClass> list = kgClassService.selectKgClassList(kgClass);
        ExcelUtil<KgClass> util = new ExcelUtil<KgClass>(KgClass.class);
        return util.exportExcel(list, "class");
    }

    /**
     * 获取班级信息详细信息
     */
    @SaCheckPermission("kg:student:class:list")
    @GetMapping(value = "/{classId}")
    public AjaxResult getInfo(@PathVariable("classId") Long classId)
    {
        return AjaxResult.success(kgClassService.selectKgClassById(classId));
    }

    /**
     * 新增班级信息
     */
    @SaCheckPermission("kg:student:class:add")
    @Log(title = "班级信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgClass kgClass)
    {
        return toAjax(kgClassService.insertKgClass(kgClass));
    }

    /**
     * 修改班级信息
     */
    @SaCheckPermission("kg:student:class:edit")
    @Log(title = "班级信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgClass kgClass)
    {
        return toAjax(kgClassService.updateKgClass(kgClass));
    }

    /**
     * 删除班级信息
     */
    @SaCheckPermission("kg:student:class:remove")
    @Log(title = "班级信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{classIds}")
    public AjaxResult remove(@PathVariable Long[] classIds)
    {
        return toAjax(kgClassService.deleteKgClassByIds(classIds));
    }
}
