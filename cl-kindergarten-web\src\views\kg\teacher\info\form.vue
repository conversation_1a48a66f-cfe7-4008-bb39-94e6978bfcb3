<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="教师编号" prop="teacherCode">
              <el-input v-model="form.teacherCode" placeholder="请输入教师编号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="教师姓名" prop="teacherName">
              <el-input v-model="form.teacherName" placeholder="请输入教师姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="form.gender">
                <el-radio label="0">男</el-radio>
                <el-radio label="1">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="教师状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="在职" value="0" />
                <el-option label="离职" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>职业信息</span>
        </div>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="学历" prop="education">
              <el-select v-model="form.education" placeholder="请选择学历" style="width: 100%">
                <el-option label="高中及以下" value="high_school" />
                <el-option label="大专" value="college" />
                <el-option label="本科" value="bachelor" />
                <el-option label="硕士" value="master" />
                <el-option label="博士" value="doctor" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专业" prop="major">
              <el-input v-model="form.major" placeholder="请输入专业" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="职位" prop="position">
              <el-select v-model="form.position" placeholder="请选择职位" style="width: 100%">
                <el-option label="园长" value="principal" />
                <el-option label="副园长" value="vice_principal" />
                <el-option label="班主任" value="head_teacher" />
                <el-option label="副班主任" value="assistant_teacher" />
                <el-option label="任课教师" value="subject_teacher" />
                <el-option label="保育员" value="nurse" />
                <el-option label="后勤" value="logistics" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="入职日期" prop="hireDate">
              <el-date-picker
                v-model="form.hireDate"
                type="date"
                placeholder="选择入职日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基本工资" prop="baseSalary">
              <el-input-number
                v-model="form.baseSalary"
                :precision="2"
                :step="100"
                :min="0"
                placeholder="请输入基本工资"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="系统用户" prop="userId">
              <el-select v-model="form.userId" placeholder="关联系统用户" style="width: 100%" clearable>
                <el-option
                  v-for="user in userList"
                  :key="user.userId"
                  :label="user.userName"
                  :value="user.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>认证信息</span>
        </div>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="人脸识别ID" prop="faceId">
              <el-input v-model="form.faceId" placeholder="人脸识别ID" readonly />
              <el-button type="text" @click="handleFaceRegister">录入人脸</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="微信绑定" prop="wechatOpenid">
              <el-input v-model="form.wechatOpenid" placeholder="微信OpenID" readonly />
              <el-button type="text" @click="handleWechatBind">绑定微信</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <div class="form-item-label">
              <label>绑定状态：</label>
              <el-tag :type="form.isWechatBound === 1 ? 'success' : 'info'">
                {{ form.isWechatBound === 1 ? '已绑定' : '未绑定' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>其他信息</span>
        </div>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <div style="text-align: center; margin-top: 30px;">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
        <el-button v-if="form.teacherId" type="info" @click="viewDetail">查看详情</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getTeacher, addTeacher, updateTeacher } from "@/api/kg/teacher/info";
import { listUser } from "@/api/kg/system/user";

export default {
  name: "TeacherForm",
  data() {
    return {
      // 表单参数
      form: {
        teacherId: undefined,
        userId: undefined,
        teacherCode: undefined,
        teacherName: undefined,
        gender: "0",
        phone: undefined,
        idCard: undefined,
        education: undefined,
        major: undefined,
        hireDate: undefined,
        position: undefined,
        baseSalary: 3000,
        faceId: undefined,
        wechatOpenid: undefined,
        isWechatBound: 0,
        wechatBindTime: undefined,
        status: "0",
        remark: undefined
      },
      // 用户列表
      userList: [],
      // 表单校验
      rules: {
        teacherCode: [
          { required: true, message: "教师编号不能为空", trigger: "blur" },
          { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" }
        ],
        teacherName: [
          { required: true, message: "教师姓名不能为空", trigger: "blur" },
          { min: 2, max: 10, message: "长度在 2 到 10 个字符", trigger: "blur" }
        ],
        gender: [
          { required: true, message: "请选择性别", trigger: "change" }
        ],
        phone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        position: [
          { required: true, message: "请选择职位", trigger: "change" }
        ],
        hireDate: [
          { required: true, message: "请选择入职日期", trigger: "change" }
        ],
        baseSalary: [
          { required: true, message: "基本工资不能为空", trigger: "blur" },
          { type: 'number', min: 0, message: "基本工资不能小于0", trigger: "blur" }
        ],
        idCard: [
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: "请输入正确的身份证号", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getUserList();
    
    // 如果是编辑模式，获取教师信息
    if (this.$route.params.teacherId) {
      this.getTeacherInfo(this.$route.params.teacherId);
    }
    
    // 如果是新增模式，生成教师编号
    if (!this.$route.params.teacherId) {
      this.generateTeacherCode();
    }
  },
  methods: {
    // 获取用户列表
    getUserList() {
      listUser().then(response => {
        this.userList = response.rows || [];
      }).catch(() => {
        // 如果获取失败，使用空数组
        this.userList = [];
      });
    },
    
    // 获取教师信息
    getTeacherInfo(teacherId) {
      getTeacher(teacherId).then(response => {
        this.form = response.data;
      });
    },
    
    // 生成教师编号
    generateTeacherCode() {
      const now = new Date();
      const year = now.getFullYear().toString().substr(-2);
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      this.form.teacherCode = `TEA${year}${month}${random}`;
    },
    
    // 人脸录入
    handleFaceRegister() {
      this.$message.info('人脸录入功能开发中...');
      // 实际实现中，这里应该调用人脸识别API
    },
    
    // 微信绑定
    handleWechatBind() {
      this.$message.info('微信绑定功能开发中...');
      // 实际实现中，这里应该显示微信绑定二维码
    },
    
    // 提交表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.teacherId != undefined) {
            updateTeacher(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.$router.push("/kg/teacher/info");
              }
            });
          } else {
            addTeacher(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.$router.push("/kg/teacher/info");
              }
            });
          }
        }
      });
    },
    
    // 取消
    cancel() {
      this.$router.push("/kg/teacher/info");
    },
    
    // 查看详情
    viewDetail() {
      this.$router.push({
        path: "/kg/teacher/info/detail",
        query: { teacherId: this.form.teacherId }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.el-card__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
}

.form-item-label {
  display: flex;
  align-items: center;
  height: 40px;
  
  label {
    margin-right: 10px;
    color: #606266;
    font-weight: 500;
  }
}
</style>
