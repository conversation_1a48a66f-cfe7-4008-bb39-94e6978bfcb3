package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.service.IKgStudentService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 幼儿信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/student")
public class KgStudentController extends BaseController
{
    @Autowired
    private IKgStudentService kgStudentService;

    /**
     * 查询幼儿信息列表
     */
    @SaCheckPermission("kg:student:info:list")
    @GetMapping("/list")
    public TableDataInfo list(KgStudent kgStudent)
    {
        startPage();
        List<KgStudent> list = kgStudentService.selectKgStudentList(kgStudent);
        return getDataTable(list);
    }

    /**
     * 导出幼儿信息列表
     */
    @SaCheckPermission("kg:student:info:list")
    @Log(title = "幼儿信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgStudent kgStudent)
    {
        List<KgStudent> list = kgStudentService.selectKgStudentList(kgStudent);
        ExcelUtil<KgStudent> util = new ExcelUtil<KgStudent>(KgStudent.class);
        return util.exportExcel(list, "student");
    }

    /**
     * 获取幼儿信息详细信息
     */
    @SaCheckPermission("kg:student:info:list")
    @GetMapping(value = "/{studentId}")
    public AjaxResult getInfo(@PathVariable("studentId") Long studentId)
    {
        return AjaxResult.success(kgStudentService.selectKgStudentById(studentId));
    }

    /**
     * 新增幼儿信息
     */
    @SaCheckPermission("kg:student:info:add")
    @Log(title = "幼儿信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgStudent kgStudent)
    {
        return toAjax(kgStudentService.insertKgStudent(kgStudent));
    }

    /**
     * 修改幼儿信息
     */
    @SaCheckPermission("kg:student:info:edit")
    @Log(title = "幼儿信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgStudent kgStudent)
    {
        return toAjax(kgStudentService.updateKgStudent(kgStudent));
    }

    /**
     * 删除幼儿信息
     */
    @SaCheckPermission("kg:student:info:remove")
    @Log(title = "幼儿信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{studentIds}")
    public AjaxResult remove(@PathVariable Long[] studentIds)
    {
        return toAjax(kgStudentService.deleteKgStudentByIds(studentIds));
    }
}
