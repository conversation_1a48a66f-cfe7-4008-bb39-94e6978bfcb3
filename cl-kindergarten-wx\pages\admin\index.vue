<template>
	<view class="admin-container">
		<!-- 头部区域 -->
		<view class="header">
			<view class="header-content">
				<view class="welcome-section">
					<text class="welcome-title">管理员后台</text>
					<text class="welcome-subtitle">欢迎使用幼儿园管理系统</text>
				</view>
				<view class="logout-btn" @click="logout">
					<text class="logout-text">退出登录</text>
				</view>
			</view>
		</view>
		
		<!-- 主要内容区域 -->
		<view class="main-content">
			<view class="content-card">
				<view class="card-header">
					<text class="card-title">系统概览</text>
				</view>
				<view class="card-body">
					<text class="placeholder-text">管理员功能正在开发中...</text>
					<text class="placeholder-desc">这里将显示系统的各项管理功能</text>
				</view>
			</view>
			
			<!-- 功能菜单区域 -->
			<view class="menu-grid">
				<view class="menu-item">
					<view class="menu-icon">👥</view>
					<text class="menu-text">用户管理</text>
				</view>
				<view class="menu-item">
					<view class="menu-icon">📊</view>
					<text class="menu-text">数据统计</text>
				</view>
				<view class="menu-item">
					<view class="menu-icon">⚙️</view>
					<text class="menu-text">系统设置</text>
				</view>
				<view class="menu-item">
					<view class="menu-icon">📝</view>
					<text class="menu-text">内容管理</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {toast, clearStorageSync, useRouter} from '@/utils/utils.js'

export default {
	data() {
		return {
			
		};
	},
	onLoad() {
		// 页面加载时可以获取管理员信息等
		console.log('管理员后台页面加载');
	},
	methods: {
		// 退出登录
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						// 清除管理员token
						clearStorageSync('admin_token');
						toast('已退出登录');
						// 跳转回登录页
						useRouter('/pages/public/login', {}, 'redirectTo');
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.admin-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 头部区域 */
.header {
	background: #ffffff;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	padding: 40rpx 0 20rpx;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 40rpx;
}

.welcome-section {
	.welcome-title {
		display: block;
		font-size: 48rpx;
		font-weight: 700;
		color: #333333;
		margin-bottom: 8rpx;
	}
	
	.welcome-subtitle {
		display: block;
		font-size: 28rpx;
		color: #666666;
	}
}

.logout-btn {
	background: linear-gradient(135deg, #ff6b6b, #ee5a52);
	padding: 16rpx 32rpx;
	border-radius: 50rpx;
	box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.3);
	
	.logout-text {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 600;
	}
}

/* 主要内容区域 */
.main-content {
	padding: 40rpx;
}

.content-card {
	background: #ffffff;
	border-radius: 20rpx;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
	margin-bottom: 40rpx;
	overflow: hidden;
}

.card-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 30rpx 40rpx;
	
	.card-title {
		color: #ffffff;
		font-size: 36rpx;
		font-weight: 600;
	}
}

.card-body {
	padding: 60rpx 40rpx;
	text-align: center;
	
	.placeholder-text {
		display: block;
		font-size: 32rpx;
		color: #333333;
		margin-bottom: 20rpx;
		font-weight: 500;
	}
	
	.placeholder-desc {
		display: block;
		font-size: 28rpx;
		color: #999999;
	}
}

/* 功能菜单网格 */
.menu-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
}

.menu-item {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 40rpx 20rpx;
	text-align: center;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
	}
}

.menu-icon {
	font-size: 60rpx;
	margin-bottom: 20rpx;
}

.menu-text {
	display: block;
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 480px) {
	.header-content {
		padding: 0 30rpx;
	}
	
	.main-content {
		padding: 30rpx;
	}
	
	.welcome-title {
		font-size: 42rpx;
	}
}
</style>
