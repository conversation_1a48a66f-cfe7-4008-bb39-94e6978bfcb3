import request from '@/utils/request'

// 查询学生考勤列表
export function listStudentAttendance(query) {
  return request({
    url: '/business/student-attendance/list',
    method: 'get',
    params: query
  })
}

// 查询学生考勤详细
export function getStudentAttendance(attendanceId) {
  return request({
    url: '/business/student-attendance/' + attendanceId,
    method: 'get'
  })
}

// 新增学生考勤
export function addStudentAttendance(data) {
  return request({
    url: '/business/student-attendance',
    method: 'post',
    data: data
  })
}

// 修改学生考勤
export function updateStudentAttendance(data) {
  return request({
    url: '/business/student-attendance',
    method: 'put',
    data: data
  })
}

// 删除学生考勤
export function delStudentAttendance(attendanceId) {
  return request({
    url: '/business/student-attendance/' + attendanceId,
    method: 'delete'
  })
}

// 导出学生考勤
export function exportStudentAttendance(query) {
  return request({
    url: '/business/student-attendance/export',
    method: 'get',
    params: query
  })
}

// 学生签到
export function studentCheckin(data) {
  return request({
    url: '/business/student-attendance/checkin',
    method: 'post',
    data: data
  })
}

// 学生签退
export function studentCheckout(data) {
  return request({
    url: '/business/student-attendance/checkout',
    method: 'post',
    data: data
  })
}

// 考勤确认
export function confirmStudentAttendance(attendanceIds) {
  return request({
    url: '/business/student-attendance/confirm',
    method: 'post',
    data: { attendanceIds: attendanceIds }
  })
}

// 缺勤登记
export function registerAbsence(data) {
  return request({
    url: '/business/student-attendance/absence',
    method: 'post',
    data: data
  })
}
