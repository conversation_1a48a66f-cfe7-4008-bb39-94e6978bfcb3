<template>
	<view class="login-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>
		
		<!-- 主要内容 -->
		<view class="content">
			<!-- Logo 区域 -->
			<view class="logo-section">
				<view class="logo-wrapper">
					<image class="logo-image" src="/static/images/logo.png" alt="Logo" />
					<view class="logo-glow"></view>
				</view>
			</view>
			
			<!-- 标题区域 -->
			<view class="title-section">
				<text class="main-title">管理员登录</text>
				<text class="subtitle">欢迎回来，请登录您的账户</text>
			</view>
			
			<!-- 登录表单 -->
			<view class="form-section">
				<view class="form-card">
					<u-form :model="form" ref="uForm" :error-type="['toast']">
						<!-- 用户名输入 -->
						<u-form-item prop="username">
							<view class="input-group">
								<view class="input-icon">
									<text class="icon">👤</text>
								</view>
								<u-input 
									v-model="form.username" 
									placeholder="请输入用户名"
									class="custom-input"
								/>
							</view>
						</u-form-item>
						
						<!-- 密码输入 -->
						<u-form-item prop="password">
							<view class="input-group">
								<view class="input-icon">
									<text class="icon">🔒</text>
								</view>
								<u-input 
									type="password" 
									v-model="form.password" 
									placeholder="请输入密码"
									class="custom-input"
								/>
							</view>
						</u-form-item>

						<!-- 验证码输入 -->
						<u-form-item prop="captcha">
							<view class="captcha-container">
								<view class="input-group captcha-input-group">
									<view class="input-icon">
										<text class="icon">🔐</text>
									</view>
									<u-input
										v-model="form.captcha"
										placeholder="验证码"
										maxlength="4"
										class="custom-input"
									/>
								</view>
								<view class="captcha-wrapper" @click="refreshCaptcha">
									<image v-if="captchaImg" :src="captchaImg" class="captcha-img" />
									<view v-else class="captcha-placeholder">
										<text class="refresh-icon">🔄</text>
										<text class="refresh-text">点击获取</text>
									</view>
								</view>
							</view>
						</u-form-item>
					</u-form>
					
					<!-- 登录按钮 -->
					<view class="button-section">
						<view @click="submit" class="login-btn">
							<text class="btn-text">立即登录</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'

export default {
	data() {
		return {
			form: {
				username: '',
				password: '',
				captcha: '',
				comcode: '79306', // 写死的租户ID
			},
			captchaImg: '',
			captchaUuid: '',
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名',
						trigger: ['change','blur'],
					}
				],
				password: [
					{
						required: true,
						message: '请输入密码',
						trigger: ['change','blur'],
					}
				],
				captcha: [
					{
						required: true,
						message: '请输入验证码',
						trigger: ['change','blur'],
					}
				]
			}
		};
	},
	onLoad() {
		// 检查是否已有管理员token
		const adminToken = getStorageSync('admin_token');
		if (adminToken) {
			// 如果有admin_token，跳转到管理员主页
			useRouter('/pages/admin/index', {}, 'redirectTo');
			return;
		}

		// 页面加载时获取验证码
		this.getCaptcha();
	},
	methods: {
		// 获取图形验证码
		getCaptcha() {
			this.$api.getCaptcha().then(res => {
				if (res.code === 200) {
					this.captchaImg = 'data:image/jpeg;base64,' + res.img;
					this.captchaUuid = res.uuid;
				} else {
					toast('获取验证码失败');
				}
			}).catch(err => {
				toast('获取验证码失败');
			});
		},

		// 刷新验证码
		refreshCaptcha() {
			this.form.captcha = '';
			this.getCaptcha();
		},
		
		submit() {
			this.$refs.uForm.validate(valid => {
				if (valid) {
					const data = {
						code: this.form.captcha,
						comcode: this.form.comcode,
						password: this.form.password,
						username: this.form.username,
						uuid: this.captchaUuid
					}

					this.$api.login(data).then(res => {
						console.log(res)
						if (res.code !== 200) {
							toast(res.msg)
							// 登录失败时刷新验证码
							this.refreshCaptcha();
						} else {
							toast('登录成功')
							// 使用admin_前缀存储管理员token
							setStorageSync('admin_token', res.token)
							// 跳转到管理员后台页面
							useRouter('/pages/admin/index', {}, 'redirectTo')
						}
					})
				} else {
					console.log('验证失败');
				}
			});
		}
	},
	// 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕
	onReady() {
		this.$refs.uForm.setRules(this.rules);
	}
};
</script>

<style lang="scss" scoped>
.login-container {
	min-height: 100vh;
	position: relative;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
	overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	
	.circle {
		position: absolute;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(10rpx);
		animation: float 6s ease-in-out infinite;
		
		&.circle-1 {
			width: 200rpx;
			height: 200rpx;
			top: 10%;
			right: 10%;
			animation-delay: 0s;
		}
		
		&.circle-2 {
			width: 150rpx;
			height: 150rpx;
			bottom: 20%;
			left: 5%;
			animation-delay: 2s;
		}
		
		&.circle-3 {
			width: 120rpx;
			height: 120rpx;
			top: 30%;
			left: 15%;
			animation-delay: 4s;
		}
	}
}

@keyframes float {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	50% { transform: translateY(-20rpx) rotate(180deg); }
}

.content {
	position: relative;
	z-index: 2;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	padding: 80rpx 40rpx 40rpx;
}

/* Logo 区域 */
.logo-section {
	display: flex;
	justify-content: center;
	margin-bottom: 60rpx;
}

.logo-wrapper {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	
	.logo-image {
		width: 100%;
		height: 100%;
		border-radius: 30rpx;
		box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);
		z-index: 2;
		position: relative;
	}
	
	.logo-glow {
		position: absolute;
		top: -10rpx;
		left: -10rpx;
		right: -10rpx;
		bottom: -10rpx;
		background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
		border-radius: 40rpx;
		z-index: 1;
		animation: glow 3s ease-in-out infinite alternate;
		filter: blur(15rpx);
		opacity: 0.7;
	}
}

@keyframes glow {
	from { transform: scale(0.95); opacity: 0.5; }
	to { transform: scale(1.05); opacity: 0.8; }
}

/* 标题区域 */
.title-section {
	text-align: center;
	margin-bottom: 80rpx;
	
	.main-title {
		display: block;
		font-size: 64rpx;
		font-weight: 700;
		color: #ffffff;
		margin-bottom: 20rpx;
		text-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.3);
		letter-spacing: 2rpx;
	}
	
	.subtitle {
		display: block;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.85);
		font-weight: 400;
		letter-spacing: 1rpx;
	}
}

/* 表单区域 */
.form-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.form-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-radius: 30rpx;
	padding: 60rpx 40rpx;
	box-shadow: 0 30rpx 60rpx rgba(0, 0, 0, 0.2);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 输入组样式 */
.input-group {
	position: relative;
	display: flex;
	align-items: center;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
	margin-bottom: 30rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	
	&:focus-within {
		border-color: #667eea;
		box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.25);
		transform: translateY(-2rpx);
	}
}

.input-icon {
	width: 80rpx;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 16rpx 0 0 16rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	
	.icon {
		font-size: 32rpx;
	}
}

/* 验证码容器 */
.captcha-container {
	display: flex;
	gap: 20rpx;
	align-items: stretch;
	
	.captcha-input-group {
		flex: 1;
		margin-bottom: 0;
	}
}

.captcha-wrapper {
	width: 200rpx;
	height: 100rpx;
	background: #ffffff;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
	border: 2rpx solid #e9ecef;
	cursor: pointer;
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
	}
	
	.captcha-img {
		width: 100%;
		height: 100%;
		border-radius: 14rpx;
	}
	
	.captcha-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
		
		.refresh-icon {
			font-size: 28rpx;
			animation: rotate 2s linear infinite;
		}
		
		.refresh-text {
			font-size: 22rpx;
			color: #6c757d;
			font-weight: 500;
		}
	}
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

/* 按钮区域 */
.button-section {
	margin-top: 40rpx;
}

.login-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50rpx;
	border: none;
	box-shadow: 0 15rpx 35rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 10rpx 25rpx rgba(102, 126, 234, 0.5);
	}

	&:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 20rpx 40rpx rgba(102, 126, 234, 0.5);
	}

	.btn-text {
		font-size: 36rpx;
		font-weight: 600;
		color: #ffffff;
		letter-spacing: 2rpx;
		text-align: center;
	}
}

/* 深层样式覆盖 */
::v-deep .u-form-item {
	margin-bottom: 0;
	
	.u-form-item__body {
		margin-bottom: 0;
	}
}

::v-deep .custom-input {
	flex: 1;
	background: transparent;
	border: none;
	padding: 30rpx 20rpx;
	font-size: 32rpx;
	color: #333333;
	border-radius: 0 16rpx 16rpx 0;
	
	&::placeholder {
		color: #9ca3af;
		font-size: 30rpx;
	}
}



/* 响应式适配 */
@media screen and (max-width: 480px) {
	.content {
		padding: 60rpx 30rpx 30rpx;
	}
	
	.form-card {
		padding: 50rpx 30rpx;
	}
	
	.main-title {
		font-size: 56rpx;
	}
}
</style>