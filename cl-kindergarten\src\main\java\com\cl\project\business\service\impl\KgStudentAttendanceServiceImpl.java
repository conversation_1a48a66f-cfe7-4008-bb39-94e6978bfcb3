package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgStudentAttendanceMapper;
import com.cl.project.business.domain.KgStudentAttendance;
import com.cl.project.business.service.IKgStudentAttendanceService;

/**
 * 学生考勤记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgStudentAttendanceServiceImpl implements IKgStudentAttendanceService 
{
    @Autowired
    private KgStudentAttendanceMapper kgStudentAttendanceMapper;

    /**
     * 查询学生考勤记录
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 学生考勤记录
     */
    @Override
    public KgStudentAttendance selectKgStudentAttendanceById(Long attendanceId)
    {
        return kgStudentAttendanceMapper.selectKgStudentAttendanceById(attendanceId);
    }

    /**
     * 查询学生考勤记录列表
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 学生考勤记录
     */
    @Override
    public List<KgStudentAttendance> selectKgStudentAttendanceList(KgStudentAttendance kgStudentAttendance)
    {
        return kgStudentAttendanceMapper.selectKgStudentAttendanceList(kgStudentAttendance);
    }

    /**
     * 新增学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    @Override
    public int insertKgStudentAttendance(KgStudentAttendance kgStudentAttendance)
    {
        kgStudentAttendance.setCreateTime(DateUtils.getNowDate());
        return kgStudentAttendanceMapper.insertKgStudentAttendance(kgStudentAttendance);
    }

    /**
     * 修改学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    @Override
    public int updateKgStudentAttendance(KgStudentAttendance kgStudentAttendance)
    {
        kgStudentAttendance.setUpdateTime(DateUtils.getNowDate());
        return kgStudentAttendanceMapper.updateKgStudentAttendance(kgStudentAttendance);
    }

    /**
     * 批量删除学生考勤记录
     * 
     * @param attendanceIds 需要删除的学生考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgStudentAttendanceByIds(Long[] attendanceIds)
    {
        return kgStudentAttendanceMapper.deleteKgStudentAttendanceByIds(attendanceIds);
    }

    /**
     * 删除学生考勤记录信息
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgStudentAttendanceById(Long attendanceId)
    {
        return kgStudentAttendanceMapper.deleteKgStudentAttendanceById(attendanceId);
    }
}
