package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgTeacherAttendanceMapper;
import com.cl.project.business.domain.KgTeacherAttendance;
import com.cl.project.business.service.IKgTeacherAttendanceService;

/**
 * 教师考勤记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgTeacherAttendanceServiceImpl implements IKgTeacherAttendanceService 
{
    @Autowired
    private KgTeacherAttendanceMapper kgTeacherAttendanceMapper;

    /**
     * 查询教师考勤记录
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 教师考勤记录
     */
    @Override
    public KgTeacherAttendance selectKgTeacherAttendanceById(Long attendanceId)
    {
        return kgTeacherAttendanceMapper.selectKgTeacherAttendanceById(attendanceId);
    }

    /**
     * 查询教师考勤记录列表
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 教师考勤记录
     */
    @Override
    public List<KgTeacherAttendance> selectKgTeacherAttendanceList(KgTeacherAttendance kgTeacherAttendance)
    {
        return kgTeacherAttendanceMapper.selectKgTeacherAttendanceList(kgTeacherAttendance);
    }

    /**
     * 新增教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    @Override
    public int insertKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance)
    {
        kgTeacherAttendance.setCreateTime(DateUtils.getNowDate());
        return kgTeacherAttendanceMapper.insertKgTeacherAttendance(kgTeacherAttendance);
    }

    /**
     * 修改教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    @Override
    public int updateKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance)
    {
        kgTeacherAttendance.setUpdateTime(DateUtils.getNowDate());
        return kgTeacherAttendanceMapper.updateKgTeacherAttendance(kgTeacherAttendance);
    }

    /**
     * 批量删除教师考勤记录
     * 
     * @param attendanceIds 需要删除的教师考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgTeacherAttendanceByIds(Long[] attendanceIds)
    {
        return kgTeacherAttendanceMapper.deleteKgTeacherAttendanceByIds(attendanceIds);
    }

    /**
     * 删除教师考勤记录信息
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgTeacherAttendanceById(Long attendanceId)
    {
        return kgTeacherAttendanceMapper.deleteKgTeacherAttendanceById(attendanceId);
    }
}
