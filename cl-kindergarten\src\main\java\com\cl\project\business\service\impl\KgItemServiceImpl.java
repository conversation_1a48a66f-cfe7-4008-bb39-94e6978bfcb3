package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgItemMapper;
import com.cl.project.business.domain.KgItem;
import com.cl.project.business.service.IKgItemService;

/**
 * 物品信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgItemServiceImpl implements IKgItemService 
{
    @Autowired
    private KgItemMapper kgItemMapper;

    /**
     * 查询物品信息
     * 
     * @param itemId 物品信息ID
     * @return 物品信息
     */
    @Override
    public KgItem selectKgItemById(Long itemId)
    {
        return kgItemMapper.selectKgItemById(itemId);
    }

    /**
     * 查询物品信息列表
     * 
     * @param kgItem 物品信息
     * @return 物品信息
     */
    @Override
    public List<KgItem> selectKgItemList(KgItem kgItem)
    {
        return kgItemMapper.selectKgItemList(kgItem);
    }

    /**
     * 新增物品信息
     * 
     * @param kgItem 物品信息
     * @return 结果
     */
    @Override
    public int insertKgItem(KgItem kgItem)
    {
        kgItem.setCreateTime(DateUtils.getNowDate());
        return kgItemMapper.insertKgItem(kgItem);
    }

    /**
     * 修改物品信息
     * 
     * @param kgItem 物品信息
     * @return 结果
     */
    @Override
    public int updateKgItem(KgItem kgItem)
    {
        kgItem.setUpdateTime(DateUtils.getNowDate());
        return kgItemMapper.updateKgItem(kgItem);
    }

    /**
     * 批量删除物品信息
     * 
     * @param itemIds 需要删除的物品信息ID
     * @return 结果
     */
    @Override
    public int deleteKgItemByIds(Long[] itemIds)
    {
        return kgItemMapper.deleteKgItemByIds(itemIds);
    }

    /**
     * 删除物品信息信息
     * 
     * @param itemId 物品信息ID
     * @return 结果
     */
    @Override
    public int deleteKgItemById(Long itemId)
    {
        return kgItemMapper.deleteKgItemById(itemId);
    }
}
