package com.cl.project.business.domain;

import java.math.BigDecimal;

import com.cl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 物品信息对象 kg_item
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 物品ID */
    private Long itemId;

    /** 物品编码 */
    @Excel(name = "物品编码")
    private String itemCode;

    /** 物品名称 */
    @Excel(name = "物品名称")
    private String itemName;

    /** 类别ID，关联kg_item_category.category_id */
    @Excel(name = "类别ID，关联kg_item_category.category_id")
    private Long categoryId;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 计量单位 */
    @Excel(name = "计量单位")
    private String unit;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /** 最低库存 */
    @Excel(name = "最低库存")
    private Long minStock;

    /** 最高库存 */
    @Excel(name = "最高库存")
    private Long maxStock;

    /** 当前库存 */
    @Excel(name = "当前库存")
    private Long currentStock;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setItemCode(String itemCode) 
    {
        this.itemCode = itemCode;
    }

    public String getItemCode() 
    {
        return itemCode;
    }
    public void setItemName(String itemName) 
    {
        this.itemName = itemName;
    }

    public String getItemName() 
    {
        return itemName;
    }
    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }
    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setUnitPrice(BigDecimal unitPrice) 
    {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice() 
    {
        return unitPrice;
    }
    public void setMinStock(Long minStock) 
    {
        this.minStock = minStock;
    }

    public Long getMinStock() 
    {
        return minStock;
    }
    public void setMaxStock(Long maxStock) 
    {
        this.maxStock = maxStock;
    }

    public Long getMaxStock() 
    {
        return maxStock;
    }
    public void setCurrentStock(Long currentStock) 
    {
        this.currentStock = currentStock;
    }

    public Long getCurrentStock() 
    {
        return currentStock;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("itemId", getItemId())
            .append("itemCode", getItemCode())
            .append("itemName", getItemName())
            .append("categoryId", getCategoryId())
            .append("specification", getSpecification())
            .append("unit", getUnit())
            .append("unitPrice", getUnitPrice())
            .append("minStock", getMinStock())
            .append("maxStock", getMaxStock())
            .append("currentStock", getCurrentStock())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
