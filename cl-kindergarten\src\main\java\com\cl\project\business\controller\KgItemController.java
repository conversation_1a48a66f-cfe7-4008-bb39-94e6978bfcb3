package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgItem;
import com.cl.project.business.service.IKgItemService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 物品信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/item")
public class KgItemController extends BaseController
{
    @Autowired
    private IKgItemService kgItemService;

    /**
     * 查询物品信息列表
     */
    @SaCheckPermission("business:item:list")
    @GetMapping("/list")
    public TableDataInfo list(KgItem kgItem)
    {
        startPage();
        List<KgItem> list = kgItemService.selectKgItemList(kgItem);
        return getDataTable(list);
    }

    /**
     * 导出物品信息列表
     */
    @SaCheckPermission("business:item:export")
    @Log(title = "物品信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgItem kgItem)
    {
        List<KgItem> list = kgItemService.selectKgItemList(kgItem);
        ExcelUtil<KgItem> util = new ExcelUtil<KgItem>(KgItem.class);
        return util.exportExcel(list, "item");
    }

    /**
     * 获取物品信息详细信息
     */
    @SaCheckPermission("business:item:query")
    @GetMapping(value = "/{itemId}")
    public AjaxResult getInfo(@PathVariable("itemId") Long itemId)
    {
        return AjaxResult.success(kgItemService.selectKgItemById(itemId));
    }

    /**
     * 新增物品信息
     */
    @SaCheckPermission("business:item:add")
    @Log(title = "物品信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgItem kgItem)
    {
        return toAjax(kgItemService.insertKgItem(kgItem));
    }

    /**
     * 修改物品信息
     */
    @SaCheckPermission("business:item:edit")
    @Log(title = "物品信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgItem kgItem)
    {
        return toAjax(kgItemService.updateKgItem(kgItem));
    }

    /**
     * 删除物品信息
     */
    @SaCheckPermission("business:item:remove")
    @Log(title = "物品信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{itemIds}")
    public AjaxResult remove(@PathVariable Long[] itemIds)
    {
        return toAjax(kgItemService.deleteKgItemByIds(itemIds));
    }
}
