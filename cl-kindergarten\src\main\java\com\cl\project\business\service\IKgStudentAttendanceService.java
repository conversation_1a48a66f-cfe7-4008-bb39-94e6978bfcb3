package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgStudentAttendance;

/**
 * 学生考勤记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgStudentAttendanceService 
{
    /**
     * 查询学生考勤记录
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 学生考勤记录
     */
    public KgStudentAttendance selectKgStudentAttendanceById(Long attendanceId);

    /**
     * 查询学生考勤记录列表
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 学生考勤记录集合
     */
    public List<KgStudentAttendance> selectKgStudentAttendanceList(KgStudentAttendance kgStudentAttendance);

    /**
     * 新增学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    public int insertKgStudentAttendance(KgStudentAttendance kgStudentAttendance);

    /**
     * 修改学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    public int updateKgStudentAttendance(KgStudentAttendance kgStudentAttendance);

    /**
     * 批量删除学生考勤记录
     * 
     * @param attendanceIds 需要删除的学生考勤记录ID
     * @return 结果
     */
    public int deleteKgStudentAttendanceByIds(Long[] attendanceIds);

    /**
     * 删除学生考勤记录信息
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 结果
     */
    public int deleteKgStudentAttendanceById(Long attendanceId);
}
