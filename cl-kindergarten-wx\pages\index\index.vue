<template>
	<view class="content">
		<view class="">
				<h1>我是首页</h1>
		</view>
	</view>
</template>
<script>
import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'
export default {
	data() {
		return {
			id: 0
		}
	},
	onLoad(op) {
		//this.id  = op.id
	},
	onShow() {
	},
	onReady() {
	},
	methods: {

	}
}
</script>
<style lang="scss" scoped>
    page{background:#f2f2f2}
	.content{padding: 20rpx;}
</style>