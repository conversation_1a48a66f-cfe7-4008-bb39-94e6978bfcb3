<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgItemMapper">
    
    <resultMap type="KgItem" id="KgItemResult">
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="categoryId"    column="category_id"    />
        <result property="specification"    column="specification"    />
        <result property="unit"    column="unit"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="minStock"    column="min_stock"    />
        <result property="maxStock"    column="max_stock"    />
        <result property="currentStock"    column="current_stock"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgItemVo">
        select item_id, item_code, item_name, category_id, specification, unit, unit_price, min_stock, max_stock, current_stock, status, com_id, create_by, create_time, update_by, update_time, remark from kg_item
    </sql>

    <select id="selectKgItemList" parameterType="KgItem" resultMap="KgItemResult">
        <include refid="selectKgItemVo"/>
        <where>  
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="unitPrice != null "> and unit_price = #{unitPrice}</if>
            <if test="minStock != null "> and min_stock = #{minStock}</if>
            <if test="maxStock != null "> and max_stock = #{maxStock}</if>
            <if test="currentStock != null "> and current_stock = #{currentStock}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgItemById" parameterType="Long" resultMap="KgItemResult">
        <include refid="selectKgItemVo"/>
        where item_id = #{itemId}
    </select>
        
    <insert id="insertKgItem" parameterType="KgItem" useGeneratedKeys="true" keyProperty="itemId">
        insert into kg_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="specification != null">specification,</if>
            <if test="unit != null">unit,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="minStock != null">min_stock,</if>
            <if test="maxStock != null">max_stock,</if>
            <if test="currentStock != null">current_stock,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unit != null">#{unit},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="minStock != null">#{minStock},</if>
            <if test="maxStock != null">#{maxStock},</if>
            <if test="currentStock != null">#{currentStock},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgItem" parameterType="KgItem">
        update kg_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="minStock != null">min_stock = #{minStock},</if>
            <if test="maxStock != null">max_stock = #{maxStock},</if>
            <if test="currentStock != null">current_stock = #{currentStock},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where item_id = #{itemId}
    </update>

    <delete id="deleteKgItemById" parameterType="Long">
        delete from kg_item where item_id = #{itemId}
    </delete>

    <delete id="deleteKgItemByIds" parameterType="String">
        delete from kg_item where item_id in 
        <foreach item="itemId" collection="array" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </delete>
    
</mapper>