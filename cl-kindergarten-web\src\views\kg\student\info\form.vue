<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="学生编号" prop="studentCode">
              <el-input v-model="form.studentCode" placeholder="请输入学生编号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="学生姓名" prop="studentName">
              <el-input v-model="form.studentName" placeholder="请输入学生姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="form.gender">
                <el-radio label="0">男</el-radio>
                <el-radio label="1">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker
                v-model="form.birthDate"
                type="date"
                placeholder="选择出生日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="所属班级" prop="classId">
              <el-select v-model="form.classId" placeholder="请选择班级" style="width: 100%">
                <el-option
                  v-for="item in classList"
                  :key="item.classId"
                  :label="item.className"
                  :value="item.classId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入园日期" prop="enrollmentDate">
              <el-date-picker
                v-model="form.enrollmentDate"
                type="date"
                placeholder="选择入园日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="学生状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="在园" value="0" />
                <el-option label="退园" value="1" />
                <el-option label="请假" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="家庭住址" prop="address">
              <el-input v-model="form.address" placeholder="请输入家庭住址" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>家长信息</span>
        </div>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="家长姓名" prop="parentName">
              <el-input v-model="form.parentName" placeholder="请输入家长姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="家长电话" prop="parentPhone">
              <el-input v-model="form.parentPhone" placeholder="请输入家长电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="微信OpenID" prop="wechatOpenid">
              <el-input v-model="form.wechatOpenid" placeholder="微信绑定ID" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="紧急联系人" prop="emergencyContact">
              <el-input v-model="form.emergencyContact" placeholder="请输入紧急联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急联系电话" prop="emergencyPhone">
              <el-input v-model="form.emergencyPhone" placeholder="请输入紧急联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="人脸识别ID" prop="faceId">
              <el-input v-model="form.faceId" placeholder="人脸识别ID" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>其他信息</span>
        </div>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <div style="text-align: center; margin-top: 30px;">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
        <el-button v-if="form.studentId" type="info" @click="viewDetail">查看详情</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getStudent, addStudent, updateStudent } from "@/api/kg/student/info";
import { listClass } from "@/api/kg/student/class";

export default {
  name: "StudentForm",
  data() {
    return {
      // 表单参数
      form: {
        studentId: undefined,
        studentCode: undefined,
        studentName: undefined,
        gender: "0",
        birthDate: undefined,
        idCard: undefined,
        phone: undefined,
        parentName: undefined,
        parentPhone: undefined,
        emergencyContact: undefined,
        emergencyPhone: undefined,
        address: undefined,
        classId: undefined,
        enrollmentDate: undefined,
        status: "0",
        faceId: undefined,
        wechatOpenid: undefined,
        remark: undefined
      },
      // 班级列表
      classList: [],
      // 表单校验
      rules: {
        studentCode: [
          { required: true, message: "学生编号不能为空", trigger: "blur" },
          { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" }
        ],
        studentName: [
          { required: true, message: "学生姓名不能为空", trigger: "blur" },
          { min: 2, max: 10, message: "长度在 2 到 10 个字符", trigger: "blur" }
        ],
        gender: [
          { required: true, message: "请选择性别", trigger: "change" }
        ],
        birthDate: [
          { required: true, message: "请选择出生日期", trigger: "change" }
        ],
        parentName: [
          { required: true, message: "家长姓名不能为空", trigger: "blur" }
        ],
        parentPhone: [
          { required: true, message: "家长电话不能为空", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        classId: [
          { required: true, message: "请选择班级", trigger: "change" }
        ],
        enrollmentDate: [
          { required: true, message: "请选择入园日期", trigger: "change" }
        ],
        idCard: [
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: "请输入正确的身份证号", trigger: "blur" }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        emergencyPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getClassList();
    
    // 如果是编辑模式，获取学生信息
    if (this.$route.params.studentId) {
      this.getStudentInfo(this.$route.params.studentId);
    }
    
    // 如果是新增模式，生成学生编号
    if (!this.$route.params.studentId) {
      this.generateStudentCode();
    }
  },
  methods: {
    // 获取班级列表
    getClassList() {
      listClass().then(response => {
        this.classList = response.rows || [];
      });
    },
    
    // 获取学生信息
    getStudentInfo(studentId) {
      getStudent(studentId).then(response => {
        this.form = response.data;
      });
    },
    
    // 生成学生编号
    generateStudentCode() {
      const now = new Date();
      const year = now.getFullYear().toString().substr(-2);
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      this.form.studentCode = `STU${year}${month}${random}`;
    },
    
    // 提交表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.studentId != undefined) {
            updateStudent(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.$router.push("/kg/student/info");
              }
            });
          } else {
            addStudent(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.$router.push("/kg/student/info");
              }
            });
          }
        }
      });
    },
    
    // 取消
    cancel() {
      this.$router.push("/kg/student/info");
    },
    
    // 查看详情
    viewDetail() {
      this.$router.push({
        path: "/kg/student/info/detail",
        query: { studentId: this.form.studentId }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.el-card__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
}
</style>
