package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgItemCategory;

/**
 * 物品类别Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgItemCategoryMapper 
{
    /**
     * 查询物品类别
     * 
     * @param categoryId 物品类别ID
     * @return 物品类别
     */
    public KgItemCategory selectKgItemCategoryById(Long categoryId);

    /**
     * 查询物品类别列表
     * 
     * @param kgItemCategory 物品类别
     * @return 物品类别集合
     */
    public List<KgItemCategory> selectKgItemCategoryList(KgItemCategory kgItemCategory);

    /**
     * 新增物品类别
     * 
     * @param kgItemCategory 物品类别
     * @return 结果
     */
    public int insertKgItemCategory(KgItemCategory kgItemCategory);

    /**
     * 修改物品类别
     * 
     * @param kgItemCategory 物品类别
     * @return 结果
     */
    public int updateKgItemCategory(KgItemCategory kgItemCategory);

    /**
     * 删除物品类别
     * 
     * @param categoryId 物品类别ID
     * @return 结果
     */
    public int deleteKgItemCategoryById(Long categoryId);

    /**
     * 批量删除物品类别
     * 
     * @param categoryIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgItemCategoryByIds(Long[] categoryIds);
}
